<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\ChequeStatement;
use App\Models\AccountSubGroup;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ProfitAndLossReportController extends Controller
{
    public function generateProfitAndLoss(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ]);

        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        try {
            // Initialize income and expense arrays
            $income = [];
            $expenses = [];

            // --- FIFO COGS LOGIC START ---
            // Get all products with opening stock and purchases
            $products = \App\Models\Product::with(['variants', 'variants.product'])->get();
            $productStockMap = [];
            foreach ($products as $product) {
                foreach ($product->variants as $variant) {
                    $key = $variant->product_variant_id;
                    $productStockMap[$key] = [
                        'opening_qty' => $variant->opening_stock_quantity ?? 0,
                        'opening_cost' => $variant->buying_cost ?? 0,
                        'purchases' => [], // Will fill below
                    ];
                }
            }
            // Get all purchases in order (FIFO)
            $purchaseItems = \App\Models\PurchaseItem::whereBetween('created_at', [$fromDate, $toDate])->orderBy('created_at')->get();
            foreach ($purchaseItems as $purchaseItem) {
                $key = $purchaseItem->product_variant_id;
                if (!isset($productStockMap[$key])) continue;
                $productStockMap[$key]['purchases'][] = [
                    'qty' => $purchaseItem->quantity,
                    'cost' => $purchaseItem->buying_cost,
                    'purchase_id' => $purchaseItem->purchase_id,
                    'batch_number' => $purchaseItem->batch_number,
                    'expiry_date' => $purchaseItem->expiry_date,
                ];
            }
            // Helper to allocate COGS FIFO
            $allocateFIFO = function($variantId, $qtyNeeded) use (&$productStockMap) {
                $details = [];
                $remaining = $qtyNeeded;
                // Opening stock first
                if ($remaining > 0 && isset($productStockMap[$variantId])) {
                    $osQty = $productStockMap[$variantId]['opening_qty'];
                    $osCost = $productStockMap[$variantId]['opening_cost'];
                    if ($osQty > 0) {
                        $useQty = min($osQty, $remaining);
                        $details[] = [
                            'source' => 'Opening Stock',
                            'qty' => $useQty,
                            'cost' => $osCost,
                            'total_cost' => $useQty * $osCost,
                        ];
                        $productStockMap[$variantId]['opening_qty'] -= $useQty;
                        $remaining -= $useQty;
                    }
                }
                // Then purchases FIFO
                while ($remaining > 0 && isset($productStockMap[$variantId]) && count($productStockMap[$variantId]['purchases']) > 0) {
                    $purchase = &$productStockMap[$variantId]['purchases'][0];
                    $useQty = min($purchase['qty'], $remaining);
                    $details[] = [
                        'source' => 'Purchase',
                        'qty' => $useQty,
                        'cost' => $purchase['cost'],
                        'total_cost' => $useQty * $purchase['cost'],
                        'purchase_id' => $purchase['purchase_id'],
                        'batch_number' => $purchase['batch_number'],
                        'expiry_date' => $purchase['expiry_date'],
                    ];
                    $purchase['qty'] -= $useQty;
                    $remaining -= $useQty;
                    if ($purchase['qty'] <= 0) {
                        array_shift($productStockMap[$variantId]['purchases']);
                    }
                }
                return $details;
            };
            // --- FIFO COGS LOGIC END ---

            // SALES/INVOICE SECTION (INCOME/EXPENSE)
            // Combine sales and invoice items for income under 'Sales/Invoice'
            $salesAndInvoices = [];
            // Sale Items
            $saleItems = \App\Models\SaleItem::with(['sale'])->whereHas('sale', function($q) use ($fromDate, $toDate) {
                $q->whereBetween('created_at', [$fromDate, $toDate]);
            })->get();
            foreach ($saleItems as $item) {
                $variantId = $item->product_variant_id;
                $qty = $item->quantity;
                $unitPrice = $item->unit_price;
                $totalSales = $unitPrice * $qty;
                $billNo = $item->sale ? $item->sale->bill_number : 'N/A';
                $salesAndInvoices[] = [
                    'type' => 'Sale',
                    'ref_no' => $billNo,
                    'date' => $item->created_at ? $item->created_at->format('Y-m-d') : 'N/A',
                    'amount' => $totalSales,
                    'details' => [
                        'type' => 'Sale',
                        'bill_no' => $billNo,
                        'product' => $item->product_name,
                        'quantity' => $qty,
                        'unit_price' => $unitPrice,
                    ],
                ];
                // Expense: COGS (FIFO)
                $fifoDetails = $allocateFIFO($variantId, $qty);
                foreach ($fifoDetails as $fd) {
                    $desc = $fd['source'] === 'Opening Stock' ? 'COGS from Opening Stock' : 'COGS from Purchase';
                    if ($fd['source'] === 'Purchase' && isset($fd['purchase_id'])) {
                        $purchase = \App\Models\Purchase::find($fd['purchase_id']);
                        $desc .= $purchase && $purchase->bill_number ? ' (Purchase Bill: ' . $purchase->bill_number . ')' : '';
                    }
                    $expenses[] = [
                        'category' => 'COGS',
                        'description' => $desc . ' (Sale Bill: ' . $billNo . ')',
                        'amount' => $fd['total_cost'],
                        'date' => $item->created_at ? $item->created_at->format('Y-m-d') : 'N/A',
                        'details' => [
                            'type' => $fd['source'],
                            'bill_no' => $billNo,
                            'product' => $item->product_name,
                            'quantity' => $fd['qty'],
                            'unit_cost' => $fd['cost'],
                        ],
                    ];
                }
            }
            // Invoice Items
            $invoiceItems = \App\Models\InvoiceItem::with(['invoice'])->whereHas('invoice', function($q) use ($fromDate, $toDate) {
                $q->whereBetween('invoice_date', [$fromDate, $toDate]);
            })->get();
            foreach ($invoiceItems as $item) {
                $variantId = $item->product_variant_id;
                $qty = $item->quantity;
                $unitPrice = $item->sales_price ?? $item->unit_price;
                $totalSales = $unitPrice * $qty;
                $invoiceNo = $item->invoice ? $item->invoice->invoice_no : 'N/A';
                $salesAndInvoices[] = [
                    'type' => 'Invoice',
                    'ref_no' => $invoiceNo,
                    'date' => $item->created_at ? $item->created_at->format('Y-m-d') : 'N/A',
                    'amount' => $totalSales,
                    'details' => [
                        'type' => 'Invoice',
                        'invoice_no' => $invoiceNo,
                        'product' => $item->description,
                        'quantity' => $qty,
                        'unit_price' => $unitPrice,
                    ],
                ];
                // Expense: COGS (FIFO)
                $fifoDetails = $allocateFIFO($variantId, $qty);
                foreach ($fifoDetails as $fd) {
                    $desc = $fd['source'] === 'Opening Stock' ? 'COGS from Opening Stock' : 'COGS from Purchase';
                    if ($fd['source'] === 'Purchase' && isset($fd['purchase_id'])) {
                        $purchase = \App\Models\Purchase::find($fd['purchase_id']);
                        $desc .= $purchase && $purchase->bill_number ? ' (Purchase Bill: ' . $purchase->bill_number . ')' : '';
                    }
                    $expenses[] = [
                        'category' => 'COGS',
                        'description' => $desc . ' (Invoice No: ' . $invoiceNo . ')',
                        'amount' => $fd['total_cost'],
                        'date' => $item->created_at ? $item->created_at->format('Y-m-d') : 'N/A',
                        'details' => [
                            'type' => $fd['source'],
                            'invoice_no' => $invoiceNo,
                            'product' => $item->description,
                            'quantity' => $fd['qty'],
                            'unit_cost' => $fd['cost'],
                        ],
                    ];
                }
            }
            // --- END SALES/INVOICE/PURCHASE LOGIC ---

            // Process PaymentMethod transactions (sales, invoices, and purchases)
            $paymentMethodTransactions = PaymentMethod::whereBetween('date', [$fromDate, $toDate])
                ->get();

            $incomeMainAccounts = ['Direct Income', 'Indirect Income', 'Sales Accounts'];
            $expenseMainAccounts = ['Direct Expenses', 'Indirect Expenses', 'Purchase Account'];

            foreach ($paymentMethodTransactions as $transaction) {
                $accountGroup = $this->getMainAccountType($transaction->account_group);
                $referenceNumber = $transaction->reference_number ?? 'N/A';

                if (strtolower($transaction->type) === 'sales' || strtolower($transaction->type) === 'invoice') {
                    $income[] = [
                        'category' => 'Sales/Invoice',
                        'description' => ucfirst($transaction->type) . ": {$referenceNumber}",
                        'amount' => abs((float)$transaction->total),
                        'date' => $transaction->date->format('Y-m-d'),
                    ];
                } elseif (strtolower($transaction->type) === 'purchase') {
                    $expenses[] = [
                        'category' => 'Purchase',
                        'description' => "Purchase: {$referenceNumber}",
                        'amount' => abs((float)$transaction->total),
                        'date' => $transaction->date->format('Y-m-d'),
                    ];
                }
            }

            // Process Payment transactions (vouchers)
            $paymentTransactions = Payment::whereBetween('payment_date', [$fromDate, $toDate])
                ->get();

            foreach ($paymentTransactions as $payment) {
                $accountGroup = $this->getMainAccountType($payment->account_type ?? 'Direct Expenses');
                $subAccountName = $this->getSubAccountName($payment->account_type);
                $description = $subAccountName ?: $accountGroup;

                // --- EXISTING LOGIC FOR LEDGER VOUCHERS ---
                if (strpos($payment->voucher_no, 'REC-') === 0 && $payment->refer_type === 'Ledger' && in_array($accountGroup, $incomeMainAccounts)) {
                    $income[] = [
                        'category' => $accountGroup,
                        'description' => "Receive Voucher: {$description} ({$payment->voucher_no})",
                        'amount' => abs((float)$payment->amount),
                        'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                    ];
                } elseif (strpos($payment->voucher_no, 'PAY-') === 0 && $payment->refer_type === 'Ledger' && in_array($accountGroup, $expenseMainAccounts)) {
                    $expenses[] = [
                        'category' => $accountGroup,
                        'description' => "Payment Voucher: {$description} ({$payment->voucher_no})",
                        'amount' => abs((float)$payment->amount),
                        'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                    ];
                }

                // --- NEW LOGIC FOR DISCOUNTS ---
                // Receive Voucher to Customer
                if (strpos($payment->voucher_no, 'REC-') === 0 && $payment->refer_type === 'Customer' && $payment->discount > 0) {
                    if (strtolower($payment->payment_method) === 'cheque') {
                        // Check if declined
                        $declinedCheque = \App\Models\ChequeStatement::where('payment_id', $payment->id)->where('status', 'declined')->first();
                        if ($declinedCheque) {
                            $income[] = [
                                'category' => 'Discount (Declined Cheque)',
                                'description' => "Discount reversed from declined cheque to customer (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        } else {
                            $expenses[] = [
                                'category' => 'Discount',
                                'description' => "Discount given to customer (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        }
                    } else {
                        $expenses[] = [
                            'category' => 'Discount',
                            'description' => "Discount given to customer (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
                // Payment Voucher to Supplier
                if (strpos($payment->voucher_no, 'PAY-') === 0 && $payment->refer_type === 'Supplier' && $payment->discount > 0) {
                    if (strtolower($payment->payment_method) === 'cheque') {
                        // Check if declined
                        $declinedCheque = \App\Models\ChequeStatement::where('payment_id', $payment->id)->where('status', 'declined')->first();
                        // Always add to income
                        $income[] = [
                            'category' => 'Discount',
                            'description' => "Discount received from supplier (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                        // If declined, also add to expense
                        if ($declinedCheque) {
                            $expenses[] = [
                                'category' => 'Discount (Declined Cheque)',
                                'description' => "Discount reversed from declined cheque to supplier (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        }
                    } else {
                        $income[] = [
                            'category' => 'Discount',
                            'description' => "Discount received from supplier (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
            }

            // Process ChequeStatement transactions (declined vouchers)
            $chequeTransactions = ChequeStatement::whereBetween('payment_date', [$fromDate, $toDate])
                ->where('status', 'declined')
                ->get();

            foreach ($chequeTransactions as $cheque) {
                $accountGroup = $this->getMainAccountType($cheque->account_group ?? 'Direct Expenses');
                $subAccountName = $this->getSubAccountName($cheque->account_group);
                $description = $subAccountName ?: $accountGroup;

                // Only include if refer_type is 'Ledger'
                if (($cheque->refer_type ?? null) === 'Ledger') {
                    if (strpos($cheque->voucher_no, 'PAY-') === 0 && in_array($accountGroup, $incomeMainAccounts)) {
                        $income[] = [
                            'category' => $accountGroup,
                            'description' => "Declined Payment Voucher: {$description} ({$cheque->voucher_no})",
                            'amount' => abs((float)$cheque->amount),
                            'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    } elseif (strpos($cheque->voucher_no, 'REC-') === 0 && in_array($accountGroup, $expenseMainAccounts)) {
                        $expenses[] = [
                            'category' => $accountGroup,
                            'description' => "Declined Receive Voucher: {$description} ({$cheque->voucher_no})",
                            'amount' => abs((float)$cheque->amount),
                            'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
            }

            // Calculate totals
            $totalIncome = array_sum(array_column($income, 'amount'));
            $totalExpenses = array_sum(array_column($expenses, 'amount'));
            $netProfitLoss = $totalIncome - $totalExpenses;

            return response()->json([
                'success' => true,
                'data' => [
                    'income' => $income,
                    'expenses' => $expenses,
                ],
                'totals' => [
                    'totalIncome' => $totalIncome,
                    'totalExpenses' => $totalExpenses,
                    'netProfitLoss' => $netProfitLoss,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating profit and loss report: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function getMainAccountTypes()
    {
        return [
            'Direct Expenses', 'Indirect Expenses', 'Indirect Income', 'Loan Liabilities',
            'Bank OD', 'Current Liabilities', 'Sundry Creditors', 'Capital Account',
            'Bank Accounts', 'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account', 'Sales Accounts', 'Cash in Hand'
        ];
    }

    private function getMainAccountType($accountGroup)
    {
        $mainAccountGroups = $this->getMainAccountTypes();

        if (in_array($accountGroup, $mainAccountGroups)) {
            return $accountGroup;
        }

        $subGroup = AccountSubGroup::where('sub_group_name', $accountGroup)->first();
        return $subGroup ? $subGroup->main_group : 'Direct Expenses';
    }

    private function isDebitAccountType($mainAccountType)
    {
        $debitAccountTypes = [
            'Direct Expenses', 'Indirect Expenses', 'Bank Accounts',
            'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account' ,'Cash in Hand'
        ];

        return in_array($mainAccountType, $debitAccountTypes);
    }

    private function getSubAccountName($accountGroup)
    {
        $subGroup = AccountSubGroup::where('sub_group_name', $accountGroup)->first();
        return $subGroup ? $subGroup->sub_group_name : null;
    }
}